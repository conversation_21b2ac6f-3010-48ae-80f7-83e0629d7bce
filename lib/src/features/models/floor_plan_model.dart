import 'dart:io';
import 'package:equatable/equatable.dart';

class FloorPlanModel extends Equatable {
  final String? nameAr;
  final String? nameEn;
  final File? image;

  const FloorPlanModel({
    this.nameAr,
    this.nameEn,
    this.image,
  });

  factory FloorPlanModel.fromJson(Map<String, dynamic> json) {
    return FloorPlanModel(
      nameAr: json['name']?['ar'] as String?,
      nameEn: json['name']?['en'] as String?,
      // Note: image is not loaded from JSON as it's a File object for uploads
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = {
      'ar': nameAr,
      'en': nameEn,
    };
    return data;
  }

  FloorPlanModel copyWith({
    String? nameAr,
    String? nameEn,
    File? image,
  }) {
    return FloorPlanModel(
      nameAr: nameAr ?? this.nameAr,
      nameEn: nameEn ?? this.nameEn,
      image: image ?? this.image,
    );
  }

  @override
  List<Object?> get props => [nameAr, nameEn, image];
}
