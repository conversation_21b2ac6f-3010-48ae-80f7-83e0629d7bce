import 'package:equatable/equatable.dart';

class ProjectPlanModel extends Equatable {
  final String? bedroomsAr;
  final String? bedroomsEn;
  final String? priceFrom;
  final String? priceTo;
  final String? spaceSizeAr;
  final String? spaceSizeEn;

  const ProjectPlanModel({
    this.bedroomsAr,
    this.bedroomsEn,
    this.priceFrom,
    this.priceTo,
    this.spaceSizeAr,
    this.spaceSizeEn,
  });

  factory ProjectPlanModel.fromJson(Map<String, dynamic> json) {
    return ProjectPlanModel(
      bedroomsAr: json['bedrooms']?['ar'] as String?,
      bedroomsEn: json['bedrooms']?['en'] as String?,
      priceFrom: json['price_from'] as String?,
      priceTo: json['price_to'] as String?,
      spaceSizeAr: json['space_size']?['ar'] as String?,
      spaceSizeEn: json['space_size']?['en'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['bedrooms'] = {
      'ar': bedroomsAr,
      'en': bedroomsEn,
    };
    data['price_from'] = priceFrom;
    data['price_to'] = priceTo;
    data['space_size'] = {
      'ar': spaceSizeAr,
      'en': spaceSizeEn,
    };
    return data;
  }

  ProjectPlanModel copyWith({
    String? bedroomsAr,
    String? bedroomsEn,
    String? priceFrom,
    String? priceTo,
    String? spaceSizeAr,
    String? spaceSizeEn,
  }) {
    return ProjectPlanModel(
      bedroomsAr: bedroomsAr ?? this.bedroomsAr,
      bedroomsEn: bedroomsEn ?? this.bedroomsEn,
      priceFrom: priceFrom ?? this.priceFrom,
      priceTo: priceTo ?? this.priceTo,
      spaceSizeAr: spaceSizeAr ?? this.spaceSizeAr,
      spaceSizeEn: spaceSizeEn ?? this.spaceSizeEn,
    );
  }

  @override
  List<Object?> get props => [
        bedroomsAr,
        bedroomsEn,
        priceFrom,
        priceTo,
        spaceSizeAr,
        spaceSizeEn,
      ];
}
