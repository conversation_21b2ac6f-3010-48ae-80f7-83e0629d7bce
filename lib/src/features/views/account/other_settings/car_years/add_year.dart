import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/core/utils/resources.dart';
import 'package:flutter/material.dart';

import '../../../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../../bloc/car_year_bloc.dart';

class AddYears extends StatefulWidget {
  const AddYears({super.key});

  @override
  _AddFeature createState() => _AddFeature();
}

class _AddFeature extends State<AddYears> {
  String? currentvalue2;
  TextEditingController yearController = TextEditingController();

  bool loading = false;
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: GlobalColors.primaryColor,
          centerTitle: true,
          title: Text(S.of(context).AddNewYear),
        ),
        body: Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                S.of(context).Year,
                style: const TextStyle(fontSize: 13),
              ),
              const SizedBox(
                height: 10,
              ),
              Container(
                height: 50,
                width: MediaQuery.of(context).size.width,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(width: 0.5, color: Colors.grey[100]!),
                    color: Colors.white),
                child: Container(
                  padding: const EdgeInsets.only(
                      left: 20, right: 20, top: 15, bottom: 15),
                  child: TextFormField(
                    controller: yearController,
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      hintText: S.of(context).Year,
                      hintStyle: const TextStyle(
                          color: Color(0xffB7B7B7), fontSize: 14),
                    ),
                  ),
                ),
              ),
              const Spacer(),
              Container(
                height: 60,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Center(
                      child: GestureDetector(
                        onTap: () async {
                          setState(() {
                            loading = true;
                          });
                          await carYearBloc.addCarYear(
                              year: yearController.text);
                          setState(() {
                            loading = false;
                          });
                          Navigator.pop(context);
                        },
                        child: Container(
                          height: 50,
                          width: MediaQuery.of(context).size.width,
                          decoration: BoxDecoration(
                              color: GlobalColors.primaryColor,
                              borderRadius: BorderRadius.circular(5)),
                          child: Container(
                            padding: const EdgeInsets.all(10),
                            child: Center(
                              child: loading
                                  ? const ADLinearProgressIndicator()
                                  : Text(
                                      S.of(context).AddNewYear,
                                      style:
                                          const TextStyle(color: Colors.white),
                                    ),
                            ),
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
