import 'package:admin_dubai/generated/l10n.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

import '../../../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../../../core/shared_widgets/snack_bar.dart';
import '../../../../../core/utils/resources.dart';
import '../../../../repository/area_repository.dart';
import '../../../../response/generalResponse.dart';

class AddArea extends StatefulWidget {
  const AddArea({super.key});

  @override
  _AddArea createState() => _AddArea();
}

class _AddArea extends State<AddArea> {
  List<String> category = ['Hotels', 'Resturants'];
  TextEditingController namearController = TextEditingController();
  TextEditingController nameenController = TextEditingController();
  AreasRepository provider = AreasRepository();
  String? currentvalue2;
  bool isLoading = false;

  submit(FormData data) async {
    if (namearController.text == "") {
      snackbar(S.of(context).enteraran);

      return;
    }
    if (nameenController.text == "") {
      snackbar(S.of(context).enterenn);

      return;
    } else {
      setState(() {
        isLoading = true;
      });

      // pr.show();
      final GeneralResponse successInformation = await provider.addarea(data);
      print("success");
      print(successInformation.code);
      // pr.hide();
      if (successInformation.code == 1) {
        Navigator.pop(context);
//
//
      } else {
        if (successInformation.msg == null) {
          snackbar(S.of(context).wrong);
        } else {
          snackbar(successInformation.msg!);
        }
      }
    }

    setState(() {
      isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
            appBar: AppBar(
              backgroundColor: GlobalColors.primaryColor,
              centerTitle: true,
              title: Text(S.of(context).AddnewArea),
            ),
            body: SingleChildScrollView(
              child: Container(
                padding: const EdgeInsets.all(20),
                height: MediaQuery.of(context).size.height - 100,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      height: 50,
                      width: MediaQuery.of(context).size.width,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          color: Colors.grey[100]!),
                      child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 5),
                          child: Text(
                            S.of(context).Basicinformation,
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          )),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    Text(
                      S.of(context).areaar,
                      style: const TextStyle(fontSize: 13),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Container(
                      height: 50,
                      width: MediaQuery.of(context).size.width,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border:
                              Border.all(width: 0.5, color: Colors.grey[100]!),
                          color: Colors.white),
                      child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 5),
                          child: TextFormField(
                            controller: namearController,
                            decoration: InputDecoration(
                                border: InputBorder.none,
                                hintText: S.of(context).areaar,
                                hintStyle: const TextStyle(
                                    color: Color(0xffB7B7B7), fontSize: 14)),
                          )),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    Text(
                      S.of(context).areaen,
                      style: const TextStyle(fontSize: 13),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Container(
                      height: 50,
                      width: MediaQuery.of(context).size.width,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border:
                              Border.all(width: 0.5, color: Colors.grey[100]!),
                          color: Colors.white),
                      child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 5),
                          child: TextFormField(
                            controller: nameenController,
                            decoration: InputDecoration(
                                border: InputBorder.none,
                                hintText: S.of(context).areaen,
                                hintStyle: const TextStyle(
                                    color: Color(0xffB7B7B7), fontSize: 14)),
                          )),
                    ),
                    const Expanded(child: SizedBox()),
                    !isLoading
                        ? Container(
                            height: 60,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                Center(
                                    child: Container(
                                        // padding: EdgeInsets.only(right: 20, left: 20),
                                        child: GestureDetector(
                                            onTap: () async {
                                              // FormData.
                                              FormData form = FormData.fromMap({
                                                'name[en]':
                                                    nameenController.text,
                                                "name[ar]":
                                                    namearController.text,
                                                // "image": image != null
                                                //     ? await MultipartFile
                                                //         .fromFile(image.path,
                                                //             filename: image.path
                                                //                 .split('/')
                                                //                 .last)
                                                //     : null,
                                                // "video": videoFile != null
                                                //     ? await MultipartFile
                                                //         .fromFile(
                                                //             videoFile.path,
                                                //             filename: videoFile
                                                //                 .path
                                                //                 .split('/')
                                                //                 .last)
                                                //     : null
                                              });
                                              submit(form);
                                            },
                                            child: Container(
                                              height: 50,
                                              width: MediaQuery.of(context)
                                                  .size
                                                  .width,
                                              decoration: BoxDecoration(
                                                  color:
                                                      GlobalColors.primaryColor,
                                                  borderRadius:
                                                      BorderRadius.circular(5)),
                                              child: Container(
                                                  padding:
                                                      const EdgeInsets.all(10),
                                                  child: Center(
                                                      child: Text(
                                                    S.of(context).AddnewArea,
                                                    style: const TextStyle(
                                                        color: Colors.white),
                                                  ))),
                                            ))))
                              ],
                            ))
                        : const ADLinearProgressIndicator()
                  ],
                ),
              ),
            )));
  }
}
