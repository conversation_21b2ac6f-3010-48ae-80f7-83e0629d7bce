import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/core/utils/resources.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

import '../../../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../../../core/shared_widgets/snack_bar.dart';
import '../../../../api_provider/area_api_provider.dart';
import '../../../../models/area.dart';
import '../../../../response/generalResponse.dart';

class EditArea extends StatefulWidget {
  final AreasModel? area;
  const EditArea({super.key, this.area});
  @override
  _EditArea createState() => _EditArea();
}

class _EditArea extends State<EditArea> {
  List<String> category = ['Hotels', 'Resturants'];
  TextEditingController namearController = TextEditingController();
  TextEditingController nameenController = TextEditingController();
  AreaApiProvider provider = AreaApiProvider();
  String? currentvalue2;
  bool isLoading = false;
  @override
  void initState() {
    super.initState();
  }

  submit(FormData data) async {
    if (namearController.text == "") {
      snackbar(S.of(context).enteraran);

      return;
    }
    if (nameenController.text == "") {
      snackbar(S.of(context).enterenn);

      return;
    } else {
      setState(() {
        isLoading = true;
      });

      final GeneralResponse successInformation = await provider.addarea(data);
      print("success");
      print(successInformation.code);
      if (successInformation.code == 1) {
        Navigator.pop(context);
//
//
      } else {
        if (successInformation.msg == null) {
          snackbar(S.of(context).wrong);
        } else {
          snackbar(successInformation.msg!);
        }
      }
    }

    setState(() {
      isLoading = false;
    });
  }

  void deleteArea(id) {
    bool ll = false;
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
        padding:
            EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
        child: Container(
            height: MediaQuery.of(context).size.height * 0.3,
            decoration: BoxDecoration(
                color: const Color(0xffF5F6F7),
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(25.0),
                    topRight: Radius.circular(25.0)),
                border: Border.all(color: Colors.black, width: 1.0)),
            child: Column(
              children: [
                const SizedBox(
                  height: 10,
                ),
                Container(height: 5, width: 50, color: const Color(0xffD2D4D6)),
                const SizedBox(
                  height: 20,
                ),
                Center(
                    child: Text(
                  S.of(context).DeleteArea,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                )),
                Container(
                  padding: const EdgeInsets.all(15),
                  child: Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10)),
                    child: Container(
                      padding: const EdgeInsets.all(15),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(
                            height: 20,
                          ),
                          const Center(
                              child: Text(
                                  "are you sure you want to delete this area")),
                          const SizedBox(
                            height: 20,
                          ),
                          Center(
                            child: Container(
                              // padding: EdgeInsets.only(right: 20, left: 20),
                              child: GestureDetector(
                                onTap: () async {
                                  ll = true;
                                  GeneralResponse successinformation =
                                      await provider.deletearea(id);

                                  if (successinformation.code == 1) {
                                    /*Navigator.pushReplacement(context,
                                                    MaterialPageRoute(
                                                        builder: (context) {
                                                  return Features();
                                                }));*/
                                    Navigator.pop(context, true);
                                  } else {
                                    if (successinformation.msg == null) {
                                      snackbar(S.of(context).wrong);
                                    } else {
                                      snackbar(successinformation.msg!);
                                    }
                                  }
                                  ll = false;

                                  // _submit(rate.toString(), _comment.text);
                                },
                                child: Container(
                                  height: 50,
                                  width: MediaQuery.of(context).size.width,
                                  decoration: BoxDecoration(
                                      color: const Color(0xffE04E4D),
                                      borderRadius: BorderRadius.circular(10)),
                                  child: !ll
                                      ? Container(
                                          padding: const EdgeInsets.all(10),
                                          child: Center(
                                              child: Text(
                                            S.of(context).Yes,
                                            style: const TextStyle(
                                                color: Colors.white),
                                          )))
                                      : const ADLinearProgressIndicator(),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            )),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
            appBar: AppBar(
              backgroundColor: GlobalColors.primaryColor,
              centerTitle: true,
              title: Text(S.of(context).AddnewArea),
              actions: [
                InkWell(
                  onTap: () {
                    deleteArea(widget.area!.id);
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    child: const Icon(
                      Icons.delete,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            body: SingleChildScrollView(
              child: Container(
                padding: const EdgeInsets.all(20),
                height: MediaQuery.of(context).size.height - 100,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      height: 50,
                      width: MediaQuery.of(context).size.width,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          color: Colors.grey[100]!),
                      child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 5),
                          child: Text(
                            S.of(context).Basicinformation,
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          )),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    Text(
                      S.of(context).areaar,
                      style: const TextStyle(fontSize: 13),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Container(
                      height: 50,
                      width: MediaQuery.of(context).size.width,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border:
                              Border.all(width: 0.5, color: Colors.grey[100]!),
                          color: Colors.white),
                      child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 5),
                          child: TextFormField(
                            controller: namearController,
                            decoration: InputDecoration(
                                border: InputBorder.none,
                                hintText: S.of(context).areaar,
                                hintStyle: const TextStyle(
                                    color: Color(0xffB7B7B7), fontSize: 14)),
                          )),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    Text(
                      S.of(context).areaen,
                      style: const TextStyle(fontSize: 13),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Container(
                      height: 50,
                      width: MediaQuery.of(context).size.width,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border:
                              Border.all(width: 0.5, color: Colors.grey[100]!),
                          color: Colors.white),
                      child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 5),
                          child: TextFormField(
                            controller: nameenController,
                            decoration: InputDecoration(
                                border: InputBorder.none,
                                hintText: S.of(context).areaen,
                                hintStyle: const TextStyle(
                                    color: Color(0xffB7B7B7), fontSize: 14)),
                          )),
                    ),
                    const Expanded(child: SizedBox()),
                    !isLoading
                        ? Container(
                            height: 60,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                Center(
                                    child: GestureDetector(
                                        onTap: () async {
                                          // FormData.
                                          FormData form = FormData.fromMap({
                                            'name_en': nameenController.text,
                                            "name_ar": namearController.text,
                                          });
                                          submit(form);
                                        },
                                        child: Container(
                                          height: 50,
                                          width:
                                              MediaQuery.of(context).size.width,
                                          decoration: BoxDecoration(
                                              color: GlobalColors.primaryColor,
                                              borderRadius:
                                                  BorderRadius.circular(5)),
                                          child: Container(
                                              padding: const EdgeInsets.all(10),
                                              child: Center(
                                                  child: Text(
                                                S.of(context).AddnewArea,
                                                style: const TextStyle(
                                                    color: Colors.white),
                                              ))),
                                        )))
                              ],
                            ))
                        : const ADLinearProgressIndicator()
                  ],
                ),
              ),
            )));
  }
}
