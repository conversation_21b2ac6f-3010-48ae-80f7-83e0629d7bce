import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/core/utils/resources.dart';
import 'package:flutter/material.dart';

import '../../../../../core/shared_widgets/ad_circular_progress_indicator.dart';
import '../../../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../../../core/shared_widgets/snack_bar.dart';
import '../../../../bloc/car_brand_bloc.dart';
import '../../../../response/car_brand_deatils.dart';

// ignore: must_be_immutable
class EditBrand extends StatefulWidget {
  String name;
  int id;
  EditBrand(this.name, this.id);
  @override
  _EditBrand createState() => _EditBrand();
}

class _EditBrand extends State<EditBrand> {
  TextEditingController arabicBrandController = TextEditingController();
  TextEditingController englishBrandController = TextEditingController();
  bool isactions = false;
  bool loading = false;
  @override
  void initState() {
    carBrandBloc.getCarBrandDeatils(id: widget.id);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: GlobalColors.primaryColor,
          centerTitle: true,
          title: Text(widget.name),
          actions: [
            Container(
                padding: const EdgeInsets.only(left: 10, right: 10),
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      isactions = !isactions;
                    });
                  },
                  child: const Icon(Icons.more_horiz, color: Colors.white),
                ))
          ],
        ),
        body: StreamBuilder<CarBrandDeatils>(
            stream: carBrandBloc.carBrandDeatils.stream,
            builder: (context, snapshot) {
              if (snapshot.hasData &&
                  snapshot.connectionState != ConnectionState.waiting) {
                if (snapshot.data!.code != 1) {
                  snackbar(snapshot.data!.msg!);
                  return const SizedBox();
                }
                arabicBrandController.text = snapshot.data!.data![0].nameAr!;
                englishBrandController.text = snapshot.data!.data![0].nameEn!;
                return Stack(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "${S.of(context).Arabic} ${S.of(context).BrandName}",
                            style: const TextStyle(fontSize: 13),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Container(
                            height: 50,
                            width: MediaQuery.of(context).size.width,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(
                                    width: 0.5, color: Colors.grey[100]!),
                                color: Colors.white),
                            child: Container(
                                padding: const EdgeInsets.only(
                                    left: 20, right: 20, top: 15, bottom: 15),
                                child: TextFormField(
                                  controller: arabicBrandController,
                                  decoration: InputDecoration(
                                      border: InputBorder.none,
                                      hintText:
                                          "${S.of(context).Arabic} ${S.of(context).BrandName}",
                                      hintStyle: const TextStyle(
                                          color: Color(0xffB7B7B7),
                                          fontSize: 14)),
                                )),
                          ),
                          Text(
                            "${S.of(context).English} ${S.of(context).BrandName}",
                            style: const TextStyle(fontSize: 13),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Container(
                            height: 50,
                            width: MediaQuery.of(context).size.width,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(
                                    width: 0.5, color: Colors.grey[100]!),
                                color: Colors.white),
                            child: Container(
                                padding: const EdgeInsets.only(
                                    left: 20, right: 20, top: 15, bottom: 15),
                                child: TextFormField(
                                  controller: englishBrandController,
                                  decoration: InputDecoration(
                                      border: InputBorder.none,
                                      hintText:
                                          "${S.of(context).English} ${S.of(context).BrandName}",
                                      hintStyle: const TextStyle(
                                          color: Color(0xffB7B7B7),
                                          fontSize: 14)),
                                )),
                          ),
                          const Spacer(),
                          StatefulBuilder(
                            builder: (BuildContext context, setStateEdit) {
                              return Container(
                                  height: 60,
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      Center(
                                          child: Container(
                                              // padding: EdgeInsets.only(right: 20, left: 20),
                                              child: GestureDetector(
                                                  onTap: () async {
                                                    setStateEdit(() {
                                                      loading = true;
                                                    });
                                                    await carBrandBloc.editCarBrand(
                                                        id: widget.id,
                                                        arabicName:
                                                            arabicBrandController
                                                                .text,
                                                        englishName:
                                                            englishBrandController
                                                                .text);
                                                    setStateEdit(() {
                                                      loading = false;
                                                    });
                                                    Navigator.pop(context);
                                                  },
                                                  child: Container(
                                                    height: 50,
                                                    width:
                                                        MediaQuery.of(context)
                                                            .size
                                                            .width,
                                                    decoration: BoxDecoration(
                                                        color: const Color(
                                                            0xff233549),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(5)),
                                                    child: Container(
                                                        padding:
                                                            const EdgeInsets
                                                                .all(10),
                                                        child: Center(
                                                            child: loading
                                                                ? const ADLinearProgressIndicator()
                                                                : Text(
                                                                    S
                                                                        .of(context)
                                                                        .SaveChanges,
                                                                    style: const TextStyle(
                                                                        color: Colors
                                                                            .white),
                                                                  ))),
                                                  ))))
                                    ],
                                  ));
                            },
                          ),
                        ],
                      ),
                    ),
                    isactions == true
                        ? Container(
                            height: MediaQuery.of(context).size.height,
                            width: MediaQuery.of(context).size.width,
                            color: Colors.grey.withOpacity(0.4),
                            child: Container(
                                padding: const EdgeInsets.only(
                                  left: 10,
                                  right: 10,
                                  bottom: 20,
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(10),
                                        color: Colors.grey[100]!,
                                      ),
                                      width: MediaQuery.of(context).size.width,
                                      child: Column(
                                        children: [
                                          const SizedBox(
                                            height: 20,
                                          ),
                                          GestureDetector(
                                              onTap: () {
                                                deletefeature();
                                              },
                                              child: Text(
                                                S.of(context).DeleteBrand,
                                                style: const TextStyle(
                                                    color: Color(0xffE04E4D),
                                                    fontSize: 18),
                                              )),
                                          const SizedBox(
                                            height: 20,
                                          ),
                                        ],
                                      ),
                                    ),
                                    const SizedBox(
                                      height: 10,
                                    ),
                                    GestureDetector(
                                        onTap: () {
                                          setState(() {
                                            isactions = !isactions;
                                          });
                                        },
                                        child: Container(
                                          height: 50,
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(10),
                                            color: Colors.white,
                                          ),
                                          width:
                                              MediaQuery.of(context).size.width,
                                          child: Center(
                                            child: Text(
                                              S.of(context).Cancel,
                                              style: const TextStyle(
                                                  color: Color(0xff007AFF),
                                                  fontSize: 18),
                                            ),
                                          ),
                                        ))
                                  ],
                                )),
                          )
                        : Container()
                  ],
                );
              } else {
                return const Center(
                  child: ADCircularProgressIndicator(),
                );
              }
            }),
      ),
    );
  }

  void deletefeature() {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        enableDrag: true,
        backgroundColor: Colors.transparent,
        builder: (context) => Padding(
              padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom),
              child: Container(
                  height: MediaQuery.of(context).size.height * 0.3,
                  decoration: BoxDecoration(
                      color: const Color(0xffF5F6F7),
                      borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(25.0),
                          topRight: Radius.circular(25.0)),
                      border: Border.all(color: Colors.black, width: 1.0)),
                  child: Column(
                    children: [
                      const SizedBox(
                        height: 10,
                      ),
                      Container(
                          height: 5, width: 50, color: const Color(0xffD2D4D6)),
                      const SizedBox(
                        height: 20,
                      ),
                      Center(
                          child: Text(
                        S.of(context).DeleteBrand,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      )),
                      StatefulBuilder(
                        builder: (BuildContext context, setStatedelete) {
                          return Container(
                            padding: const EdgeInsets.all(15),
                            child: Container(
                              decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(10)),
                              child: Container(
                                padding: const EdgeInsets.all(15),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const SizedBox(
                                      height: 20,
                                    ),
                                    Text(S
                                        .of(context)
                                        .Areyousureyouwanttodeletethisbrand),
                                    const SizedBox(
                                      height: 20,
                                    ),
                                    Center(
                                        child: GestureDetector(
                                            onTap: () async {
                                              setStatedelete(() {
                                                loading = true;
                                              });
                                              await carBrandBloc.deleteCarBrand(
                                                  id: widget.id);
                                              setStatedelete(() {
                                                loading = false;
                                              });
                                              Navigator.pop(context);
                                              Navigator.pop(context);
                                            },
                                            child: Container(
                                              height: 50,
                                              width: MediaQuery.of(context)
                                                  .size
                                                  .width,
                                              decoration: BoxDecoration(
                                                  color:
                                                      const Color(0xffE04E4D),
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          10)),
                                              child: Container(
                                                  padding:
                                                      const EdgeInsets.all(10),
                                                  child: Center(
                                                      child: loading
                                                          ? const ADLinearProgressIndicator()
                                                          : Text(
                                                              S
                                                                  .of(context)
                                                                  .yesDeletebrand,
                                                              style: const TextStyle(
                                                                  color: Colors
                                                                      .white),
                                                            ))),
                                            ))),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                  )),
            ));
  }
}
