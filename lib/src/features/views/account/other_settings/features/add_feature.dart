import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/features/models/main_category_model.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

import '../../../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../../../core/shared_widgets/snack_bar.dart';
import '../../../../../core/utils/resources.dart';
import '../../../../api_provider/other_settings_api_provider.dart';
import '../../../../response/generalResponse.dart';

class AddFeature extends StatefulWidget {
  @override
  _AddFeature createState() => _AddFeature();
}

class _AddFeature extends State<AddFeature> {
  // List<String> category = [
  //   'hotels',
  //   'holidayHomes',
  //   'carRent',
  //   'Restaurants',
  //   'shops',
  //   'activities',
  //   'places',
  //   'luxury',
  // ];
  MainCategoryModel? currentvalue2;
  TextEditingController namearController = TextEditingController();
  TextEditingController nameenController = TextEditingController();
  OtherSettingsApiProvider provider = OtherSettingsApiProvider();
  bool isLoading = false;
  submit(FormData data) async {
    if (namearController.text == "") {
      snackbar(S.of(context).enteraran);

      return;
    }
    if (nameenController.text == "") {
      snackbar(S.of(context).enterenn);

      return;
    }

    setState(() {
      isLoading = true;
    });

    final GeneralResponse successInformation = await provider.addfeature(data);
    print("success");
    print(successInformation.code);
    if (successInformation.code == 1) {
      Navigator.pop(context, true);
    } else {
      if (successInformation.msg == null) {
        snackbar(S.of(context).wrong);
      } else {
        snackbar(successInformation.msg!);
      }
    }

    setState(() {
      isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        backgroundColor: GlobalColors.primaryColor,
        centerTitle: true,
        title: Text(S.of(context).AddNewFeature),
      ),
      body: Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              S.of(context).feaen,
              style: const TextStyle(fontSize: 13),
            ),
            const SizedBox(
              height: 10,
            ),
            Container(
              height: 50,
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(width: 0.5, color: Colors.grey[100]!),
                  color: Colors.white),
              child: Container(
                  padding: const EdgeInsets.only(
                      left: 20, right: 20, top: 15, bottom: 15),
                  child: TextFormField(
                    controller: nameenController,
                    decoration: InputDecoration(
                        border: InputBorder.none,
                        hintText: S.of(context).feaen,
                        hintStyle: const TextStyle(
                            color: Color(0xffB7B7B7), fontSize: 14)),
                  )),
            ),
            const SizedBox(
              height: 20,
            ),
            Text(
              S.of(context).feaar,
              style: const TextStyle(fontSize: 13),
            ),
            const SizedBox(
              height: 10,
            ),
            Container(
              height: 50,
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(width: 0.5, color: Colors.grey[100]!),
                  color: Colors.white),
              child: Container(
                  padding: const EdgeInsets.only(
                      left: 20, right: 20, top: 15, bottom: 15),
                  child: TextFormField(
                    controller: namearController,
                    decoration: InputDecoration(
                        border: InputBorder.none,
                        hintText: S.of(context).feaar,
                        hintStyle: const TextStyle(
                            color: Color(0xffB7B7B7), fontSize: 14)),
                  )),
            ),
            const Spacer(),
            !isLoading
                ? Container(
                    height: 60,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Center(
                            child: Container(
                                // padding: EdgeInsets.only(right: 20, left: 20),
                                child: GestureDetector(
                                    onTap: () async {
                                      FormData form = FormData.fromMap({
                                        'name[en]': nameenController.text,
                                        "name[ar]": namearController.text,
                                        "category": 8,
                                      });
                                      submit(form);
                                    },
                                    child: Container(
                                      height: 50,
                                      width: MediaQuery.of(context).size.width,
                                      decoration: BoxDecoration(
                                          color: GlobalColors.primaryColor,
                                          borderRadius:
                                              BorderRadius.circular(5)),
                                      child: Container(
                                          padding: const EdgeInsets.all(10),
                                          child: Center(
                                              child: Text(
                                            S.of(context).AddNewFeature,
                                            style: const TextStyle(
                                                color: Colors.white),
                                          ))),
                                    ))))
                      ],
                    ))
                : const ADLinearProgressIndicator()
          ],
        ),
      ),
    ));
  }
}
