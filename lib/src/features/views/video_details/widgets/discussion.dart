import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../../generated/l10n.dart';
import '../../../../core/shared_widgets/ad_circular_progress_indicator.dart';
import '../../../../core/shared_widgets/ad_linear_progress_indicator.dart';
import '../../../../core/shared_widgets/snack_bar.dart';
import '../../../bloc/discussions_bloc.dart';
import '../../../response/discussions_response.dart';
import '../../../response/generalResponse.dart';
import '../video_details.dart';

void discussion(
  BuildContext context, {
  required int? id,
  required String? categoryName,
  required isLoading,
  required editCommentId,
}) async {
  SharedPreferences _prefs = await SharedPreferences.getInstance();

  var currentUserId = _prefs.getInt('user_id');
  discussionsBloc.discussionStreamController.sink.add(null);
  discussionsBloc.getDiscussions(
      id: id, page: 1, size: 50, categoryName: categoryName);
  // ignore: use_build_context_synchronously
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    enableDrag: true,
    backgroundColor: Colors.transparent,
    builder: (context) {
      return Builder(builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, setState3) {
            return Padding(
                padding: EdgeInsets.only(
                    bottom: MediaQuery.of(context).viewInsets.bottom),
                child: Container(
                  height: MediaQuery.of(context).size.height * 0.70,
                  decoration: BoxDecoration(
                      color: const Color(0xffF5F6F7),
                      borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(25.0),
                          topRight: Radius.circular(25.0)),
                      border: Border.all(color: Colors.black, width: 1.0)),
                  child: Column(
                    children: [
                      const SizedBox(
                        height: 10,
                      ),
                      Container(
                          height: 5, width: 50, color: const Color(0xffD2D4D6)),
                      const SizedBox(
                        height: 20,
                      ),
                      Center(
                          child: Text(
                        S.of(context).Discussion,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      )),
                      const SizedBox(
                        height: 10,
                      ),
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.all(15),
                          margin: const EdgeInsets.all(15),
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(15)),
                          child: Column(
                            children: [
                              Expanded(
                                child: StreamBuilder<DiscussionsResponse?>(
                                  stream: discussionsBloc
                                      .discussionStreamController.stream,
                                  builder: (context, snapshot) {
                                    if (snapshot.hasData &&
                                        snapshot.connectionState !=
                                            ConnectionState.waiting) {
                                      if (snapshot.data!.code != 1) {
                                        snackbar(snapshot.data!.msg!);
                                        return const SizedBox();
                                      }
                                      if (snapshot.data!.data!.isEmpty) {
                                        return Center(
                                            child: Text(
                                                S.of(context).Therearenoitems));
                                      }
                                      for (var i = 0;
                                          i < snapshot.data!.data!.length;
                                          i++) {
                                        replyControllers
                                            .add(TextEditingController());

                                        ;
                                      }
                                      return ListView.builder(
                                        shrinkWrap: true,
                                        itemCount: snapshot.data!.data!.length,
                                        itemBuilder:
                                            (BuildContext context, int index) {
                                          List<DiscussionData> data =
                                              snapshot.data!.data!;
                                          replyControllers[index].text =
                                              data[index].reply ?? '';
                                          return Container(
                                            padding: const EdgeInsets.all(15),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Row(
                                                  children: [
                                                    Container(
                                                        height: 50,
                                                        width: 50,
                                                        decoration:
                                                            BoxDecoration(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(50),
                                                          color: const Color(
                                                              0xffE4E4E4),
                                                        ),
                                                        child: const Center(
                                                            child: Text(
                                                          'LR',
                                                          style: TextStyle(
                                                              color: Color(
                                                                  0xffB7B7B7)),
                                                        ))),
                                                    const SizedBox(
                                                      width: 20,
                                                    ),
                                                    Expanded(
                                                      child: Text(
                                                          data[index]
                                                                  .fullname ??
                                                              '',
                                                          style: const TextStyle(
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold)),
                                                    ),
                                                    // Spacer(),
                                                    DropdownButton<String>(
                                                      elevation: 10,
                                                      underline:
                                                          const SizedBox(),
                                                      icon: const Icon(
                                                        Icons
                                                            .more_horiz_rounded,
                                                        color: Colors.black,
                                                        size: 30,
                                                      ),
                                                      items: [
                                                        if (data[index]
                                                                .userId ==
                                                            currentUserId)
                                                          DropdownMenuItem<
                                                              String>(
                                                            value: S
                                                                .of(context)
                                                                .Editcomment,
                                                            child:
                                                                StatefulBuilder(
                                                              builder: (BuildContext
                                                                      context,
                                                                  deleteState) {
                                                                return GestureDetector(
                                                                  onTap:
                                                                      () async {
                                                                    commentController
                                                                        .text = data[
                                                                            index]
                                                                        .comment!;
                                                                    setState3(
                                                                        () {
                                                                      editCommentId =
                                                                          data[index]
                                                                              .id!;
                                                                    });
                                                                    Navigator.pop(
                                                                        context);
                                                                  },
                                                                  child: isLoading
                                                                      ? const ADLinearProgressIndicator()
                                                                      : Text(S
                                                                          .of(context)
                                                                          .Editcomment),
                                                                );
                                                              },
                                                            ),
                                                          ),
                                                        DropdownMenuItem<
                                                            String>(
                                                          value: S
                                                              .of(context)
                                                              .DeleteComment,
                                                          child:
                                                              StatefulBuilder(
                                                            builder: (BuildContext
                                                                    context,
                                                                deleteState) {
                                                              return GestureDetector(
                                                                onTap:
                                                                    () async {
                                                                  deleteState(
                                                                      () {
                                                                    isLoading =
                                                                        true;
                                                                  });
                                                                  var response = await discussionsBloc
                                                                      .deleteDiscussions(
                                                                          id: data[index]
                                                                              .id, // todo : replace id
                                                                          index:
                                                                              index);

                                                                  if (response
                                                                          .code !=
                                                                      1) {
                                                                    snackbar(
                                                                        "Something not working");
                                                                  }
                                                                  deleteState(
                                                                      () {
                                                                    isLoading =
                                                                        false;
                                                                  });
                                                                  Navigator.pop(
                                                                      context);
                                                                },
                                                                child: isLoading
                                                                    ? const ADLinearProgressIndicator()
                                                                    : Text(S
                                                                        .of(context)
                                                                        .DeleteComment),
                                                              );
                                                            },
                                                          ),
                                                        ),
                                                      ],
                                                      onChanged: (value) {},
                                                    ),
                                                  ],
                                                ),
                                                const SizedBox(
                                                  height: 10,
                                                ),
                                                Text(
                                                  data[index].comment!,
                                                  softWrap: true,
                                                ),
                                                const SizedBox(height: 5),
                                                StatefulBuilder(
                                                  builder:
                                                      (BuildContext context,
                                                          setState3) {
                                                    return Row(
                                                      children: [
                                                        Expanded(
                                                          child: Container(
                                                            height: 50,
                                                            decoration: BoxDecoration(
                                                                color: Colors
                                                                    .white,
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            3)),
                                                            child: Container(
                                                              decoration:
                                                                  BoxDecoration(
                                                                borderRadius:
                                                                    const BorderRadius
                                                                            .all(
                                                                        Radius.circular(
                                                                            5)),
                                                                border: Border.all(
                                                                    color: Colors
                                                                        .black12,
                                                                    width: 1.0),
                                                              ),
                                                              child:
                                                                  TextFormField(
                                                                controller:
                                                                    replyControllers[
                                                                        index],
                                                                decoration:
                                                                    InputDecoration(
                                                                  prefixText:
                                                                      "Reply : ",
                                                                  suffixIcon:
                                                                      Padding(
                                                                    padding:
                                                                        const EdgeInsets.all(
                                                                            10),
                                                                    child:
                                                                        InkWell(
                                                                      onTap:
                                                                          () async {
                                                                        setState3(
                                                                            () {
                                                                          isLoading =
                                                                              true;
                                                                        });
                                                                        if (data[index].reply ==
                                                                            replyControllers[index].text) {
                                                                          snackbar(
                                                                              "You have not made any changes");
                                                                          setState3(
                                                                              () {
                                                                            isLoading =
                                                                                false;
                                                                          });
                                                                          return;
                                                                        }
                                                                        var response = await discussionsBloc.replycommentdiscussion(
                                                                            id: data[index]
                                                                                .id,
                                                                            reply:
                                                                                replyControllers[index].text);

                                                                        if (response.code !=
                                                                            1) {
                                                                          replyControllers[index].text =
                                                                              data[index].reply!;

                                                                          snackbar(
                                                                              "Something not working");
                                                                        } else {
                                                                          data[index].reply =
                                                                              replyControllers[index].text;
                                                                          Fluttertoast.showToast(
                                                                              msg: "reply added successfully",
                                                                              toastLength: Toast.LENGTH_LONG,
                                                                              gravity: ToastGravity.BOTTOM,
                                                                              timeInSecForIosWeb: 3,
                                                                              backgroundColor: Colors.green,
                                                                              textColor: Colors.white,
                                                                              fontSize: 16.0);
                                                                        }
                                                                        setState3(
                                                                            () {
                                                                          isLoading =
                                                                              false;
                                                                        });
                                                                      },
                                                                      child:
                                                                          Container(
                                                                        height:
                                                                            30,
                                                                        width:
                                                                            30,
                                                                        decoration:
                                                                            BoxDecoration(
                                                                          color:
                                                                              Colors.grey[100]!,
                                                                          borderRadius:
                                                                              BorderRadius.circular(30),
                                                                        ),
                                                                        child:
                                                                            Center(
                                                                          child: isLoading
                                                                              ? const ADLinearProgressIndicator()
                                                                              : Icon(editCommentId != -1 ? Icons.done : Icons.send, size: 18),
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ),
                                                                  contentPadding:
                                                                      const EdgeInsets
                                                                              .only(
                                                                          left:
                                                                              20,
                                                                          right:
                                                                              20,
                                                                          top:
                                                                              10),
                                                                  border:
                                                                      InputBorder
                                                                          .none,
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                        const SizedBox(
                                                            width: 8),
                                                        StatefulBuilder(
                                                          builder: (BuildContext
                                                                  context,
                                                              setState4) {
                                                            return Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .all(10),
                                                              child: InkWell(
                                                                onTap:
                                                                    () async {
                                                                  setState4(() {
                                                                    isLoading =
                                                                        true;
                                                                  });

                                                                  var response =
                                                                      await discussionsBloc
                                                                          .deletediscussion(
                                                                    id: data[
                                                                            index]
                                                                        .id,
                                                                  );

                                                                  if (response
                                                                          .code !=
                                                                      1) {
                                                                    replyControllers[
                                                                            index]
                                                                        .text = data[
                                                                            index]
                                                                        .reply!;

                                                                    // snackbar(
                                                                    //     "Something not working");
                                                                  } else {
                                                                    data[index]
                                                                        .reply = "";
                                                                    Fluttertoast.showToast(
                                                                        msg:
                                                                            "reply deleted successfully",
                                                                        toastLength:
                                                                            Toast
                                                                                .LENGTH_LONG,
                                                                        gravity:
                                                                            ToastGravity
                                                                                .BOTTOM,
                                                                        timeInSecForIosWeb:
                                                                            3,
                                                                        backgroundColor:
                                                                            Colors
                                                                                .green,
                                                                        textColor:
                                                                            Colors
                                                                                .white,
                                                                        fontSize:
                                                                            16.0);
                                                                  }
                                                                  setState4(() {
                                                                    isLoading =
                                                                        false;
                                                                  });
                                                                },
                                                                child:
                                                                    Container(
                                                                  height: 30,
                                                                  width: 30,
                                                                  decoration:
                                                                      BoxDecoration(
                                                                    color: Colors
                                                                            .grey[
                                                                        100],
                                                                    borderRadius:
                                                                        BorderRadius.circular(
                                                                            30),
                                                                  ),
                                                                  child: Center(
                                                                    child: isLoading
                                                                        ? const ADLinearProgressIndicator()
                                                                        : const Icon(
                                                                            Icons
                                                                                .delete,
                                                                            size:
                                                                                18),
                                                                  ),
                                                                ),
                                                              ),
                                                            );
                                                          },
                                                        ),
                                                      ],
                                                    );
                                                  },
                                                ),
                                                Container(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          left: 10, right: 10),
                                                  child: Divider(
                                                    color: Colors.grey[100]!,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          );
                                        },
                                      );
                                    } else {
                                      return ADCircularProgressIndicator();
                                    }
                                  },
                                ),
                              ),
                              StatefulBuilder(
                                builder: (BuildContext context, setState2) {
                                  return Container(
                                    height: 50,
                                    decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(3)),
                                    child: Container(
                                      decoration: BoxDecoration(
                                        borderRadius: const BorderRadius.all(
                                            Radius.circular(5)),
                                        border: Border.all(
                                            color: Colors.black12, width: 1.0),
                                      ),
                                      child: TextFormField(
                                        controller: commentController,
                                        decoration: InputDecoration(
                                          suffixIcon: Padding(
                                            padding: const EdgeInsets.only(
                                                top: 10,
                                                bottom: 10,
                                                right: 10,
                                                left: 10),
                                            child: InkWell(
                                              onTap: () async {
                                                try {
                                                  if (editCommentId == -1) {
                                                    setState2(() {
                                                      isLoading = true;
                                                    });
                                                    GeneralResponse response =
                                                        await discussionsBloc
                                                            .sendDiscussions(
                                                                id: id,
                                                                comment:
                                                                    commentController
                                                                        .text,
                                                                categoryName:
                                                                    categoryName);

                                                    if (response.code != 1) {
                                                      snackbar(
                                                          response.msg ?? '');
                                                    }

                                                    setState2(() {
                                                      commentController.clear();

                                                      isLoading = false;
                                                    });
                                                  } else {
                                                    setState2(() {
                                                      isLoading = true;
                                                    });

                                                    GeneralResponse response =
                                                        await discussionsBloc
                                                            .editComment(
                                                                id:
                                                                    editCommentId,
                                                                comment:
                                                                    commentController
                                                                        .text,
                                                                categoryName:
                                                                    categoryName);

                                                    if (response.code != 1) {
                                                      snackbar(
                                                          response.msg ?? '');
                                                    } else {
                                                      discussionsBloc
                                                          .discussionStreamController
                                                          .sink
                                                          .add(null);
                                                      discussionsBloc
                                                          .getDiscussions(
                                                              id: id,
                                                              page: 1,
                                                              size: 50,
                                                              categoryName:
                                                                  categoryName);
                                                    }

                                                    setState2(() {
                                                      commentController.clear();
                                                      isLoading = false;
                                                    });
                                                    setState3(() {
                                                      editCommentId = -1;
                                                    });
                                                  }
                                                } catch (e) {
                                                  isLoading = false;
                                                }
                                              },
                                              child: Container(
                                                height: 30,
                                                width: 30,
                                                decoration: BoxDecoration(
                                                  color: Colors.grey[100]!,
                                                  borderRadius:
                                                      BorderRadius.circular(30),
                                                ),
                                                child: Center(
                                                  child: isLoading
                                                      ? const LinearProgressIndicator()
                                                      : Icon(
                                                          editCommentId != -1
                                                              ? Icons.done
                                                              : Icons.send,
                                                          size: 18),
                                                ),
                                              ),
                                            ),
                                          ),
                                          contentPadding: const EdgeInsets.only(
                                              left: 20, right: 20, top: 10),
                                          hintText:
                                              S.of(context).Jointhediscussion,
                                          hintStyle: const TextStyle(
                                              color: Colors.grey, fontSize: 14),
                                          border: InputBorder.none,
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ));
          },
        );
      });
    },
  );
}
