import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_quill/flutter_quill.dart';

import '../../../../../../generated/l10n.dart';
import '../../../../../core/utils/resources.dart';

class DescriptionsSection extends HookWidget {
  final ValueNotifier<String?> arabicDescription;
  final ValueNotifier<String?> englishDescription;

  const DescriptionsSection({
    super.key,
    required this.arabicDescription,
    required this.englishDescription,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Arabic Description
        Text(
          S.of(context).desara,
          style: const TextStyle(fontSize: 13),
        ),
        const SizedBox(height: 10),
        GestureDetector(
          onTap: () => _openDescriptionEditor(
            context,
            isArabic: true,
            currentText: arabicDescription.value,
            onSave: (text) => arabicDescription.value = text,
          ),
          child: Container(
            width: MediaQuery.of(context).size.width,
            padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(width: 0.5, color: Colors.grey[300]!),
              color: Colors.white,
            ),
            child: Text(
              arabicDescription.value?.isEmpty ?? true
                  ? S.of(context).enterArabicDescription
                  : S.of(context).enterArabicDescription,
              style: TextStyle(
                color: arabicDescription.value?.isEmpty ?? true
                    ? const Color(0xffB7B7B7)
                    : Colors.black,
                fontSize: 14,
              ),
            ),
          ),
        ),

        // Arabic Description Saved Indicator
        if (arabicDescription.value?.isNotEmpty == true)
          Padding(
            padding: const EdgeInsets.only(top: 10),
            child: Container(
              padding: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Colors.green[50],
                border: Border.all(color: Colors.green[200]!),
              ),
              child: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.green),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Text(
                      S.of(context).arabicDescriptionSaved,
                      style: const TextStyle(color: Colors.green),
                    ),
                  ),
                ],
              ),
            ),
          ),

        const SizedBox(height: 20),

        // English Description
        Text(
          S.of(context).deaen,
          style: const TextStyle(fontSize: 13),
        ),
        const SizedBox(height: 10),
        GestureDetector(
          onTap: () => _openDescriptionEditor(
            context,
            isArabic: false,
            currentText: englishDescription.value,
            onSave: (text) => englishDescription.value = text,
          ),
          child: Container(
            width: MediaQuery.of(context).size.width,
            padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(width: 0.5, color: Colors.grey[300]!),
              color: Colors.white,
            ),
            child: Text(
              englishDescription.value?.isEmpty ?? true
                  ? S.of(context).enterEnglishDescription
                  : S.of(context).enterEnglishDescription,
              style: TextStyle(
                color: englishDescription.value?.isEmpty ?? true
                    ? const Color(0xffB7B7B7)
                    : Colors.black,
                fontSize: 14,
              ),
            ),
          ),
        ),

        // English Description Saved Indicator
        if (englishDescription.value?.isNotEmpty == true)
          Padding(
            padding: const EdgeInsets.only(top: 10),
            child: Container(
              padding: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Colors.green[50],
                border: Border.all(color: Colors.green[200]!),
              ),
              child: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.green),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Text(
                      S.of(context).englishDescriptionSaved,
                      style: const TextStyle(color: Colors.green),
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  void _openDescriptionEditor(
    BuildContext context, {
    required bool isArabic,
    required String? currentText,
    required Function(String) onSave,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _DescriptionEditorSheet(
        isArabic: isArabic,
        currentText: currentText,
        onSave: onSave,
      ),
    );
  }
}

class _DescriptionEditorSheet extends HookWidget {
  final bool isArabic;
  final String? currentText;
  final Function(String) onSave;

  const _DescriptionEditorSheet({
    required this.isArabic,
    required this.currentText,
    required this.onSave,
  });

  @override
  Widget build(BuildContext context) {
    final controller = useMemoized(() {
      final quillController = QuillController.basic();
      if (currentText?.isNotEmpty == true) {
        // Set initial text if available
        quillController.document = Document()..insert(0, currentText!);
      }
      return quillController;
    });

    return Container(
      height: MediaQuery.of(context).size.height * 0.92, // 3/4 of screen
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              border:
                  Border(bottom: BorderSide(color: Colors.grey, width: 0.2)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  isArabic
                      ? S.of(context).enterArabicDescription
                      : S.of(context).enterEnglishDescription,
                  style: const TextStyle(
                      fontSize: 18, fontWeight: FontWeight.bold),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),

          // Toolbar
          QuillSimpleToolbar(
            controller: controller,
            config: const QuillSimpleToolbarConfig(
              color: GlobalColors.primaryColor,
            ),
          ),

          // Editor
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: QuillEditor.basic(
                controller: controller,
                config: const QuillEditorConfig(),
              ),
            ),
          ),

          // Action Buttons
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              border: Border(top: BorderSide(color: Colors.grey, width: 0.2)),
            ),
            child: Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey[300],
                      foregroundColor: Colors.black,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(S.of(context).cancel),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      final text = controller.document.toPlainText();
                      onSave(text);
                      Navigator.pop(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: GlobalColors.primaryColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(S.of(context).save),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
