import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../../../../../generated/l10n.dart';
import '../../../../../core/shared_widgets/ad_file_picker.dart';
import '../../../../../core/utils/resources.dart';
import '../../../../models/floor_plan_model.dart';

class FloorPlansSection extends HookWidget {
  final ValueNotifier<List<FloorPlanModel>> floorPlans;

  const FloorPlansSection({
    super.key,
    required this.floorPlans,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Floor Plans List with Drag and Drop
        ReorderableListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: floorPlans.value.length,
          onReorder: (oldIndex, newIndex) {
            if (newIndex > oldIndex) {
              newIndex -= 1;
            }
            final List<FloorPlanModel> newList = List.from(floorPlans.value);
            final FloorPlanModel item = newList.removeAt(oldIndex);
            newList.insert(newIndex, item);
            floorPlans.value = newList;
          },
          itemBuilder: (context, index) {
            final plan = floorPlans.value[index];
            return _buildFloorPlanItem(context, index, plan);
          },
        ),

        const SizedBox(height: 10),

        // Add New Floor Plan Button
        SizedBox(
          height: 45,
          width: MediaQuery.of(context).size.width,
          child: ElevatedButton.icon(
            onPressed: () {
              floorPlans.value = [
                ...floorPlans.value,
                FloorPlanModel(
                  id: DateTime.now().millisecondsSinceEpoch.toString(),
                ),
              ];
            },
            icon: const Icon(Icons.add, size: 16, color: Colors.white),
            label: Text(
              S.of(context).addFloorPlan,
              style: const TextStyle(color: Colors.white),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: GlobalColors.primaryColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFloorPlanItem(
      BuildContext context, int index, FloorPlanModel plan) {
    return HookBuilder(
      key: ValueKey(
          plan.nameEn), // Use unique item ID as key for proper reordering
      builder: (context) {
        final nameArController =
            useTextEditingController(text: plan.nameAr ?? '');
        final nameEnController =
            useTextEditingController(text: plan.nameEn ?? '');
        // Initialize selectedImage with current plan's image
        final selectedImage = useState<File?>(plan.image);

        // Sync selectedImage with plan.image when plan changes
        useEffect(() {
          selectedImage.value = plan.image;
          return null;
        }, [plan.image]);

        // Update the plan when controllers change
        useEffect(() {
          void updatePlan() {
            final updatedPlan = plan.copyWith(
              nameAr: nameArController.text,
              nameEn: nameEnController.text,
              image: selectedImage.value,
            );
            final newList = List<FloorPlanModel>.from(floorPlans.value);
            if (index < newList.length) {
              newList[index] = updatedPlan;
              floorPlans.value = newList;
            }
          }

          nameArController.addListener(updatePlan);
          nameEnController.addListener(updatePlan);
          return () {
            nameArController.removeListener(updatePlan);
            nameEnController.removeListener(updatePlan);
          };
        }, []);

        return Container(
          margin: const EdgeInsets.only(bottom: 15),
          padding: const EdgeInsets.all(15),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with drag handle and delete button
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.drag_handle, color: Colors.grey),
                      const SizedBox(width: 8),
                      Text(
                        'Floor Plan ${index + 1}',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  if (floorPlans.value.length > 1)
                    IconButton(
                      onPressed: () {
                        final newList =
                            List<FloorPlanModel>.from(floorPlans.value);
                        newList.removeAt(index);
                        floorPlans.value = newList;
                      },
                      icon: const Icon(Icons.delete, color: Colors.red),
                    ),
                ],
              ),
              const SizedBox(height: 10),

              // Name Arabic
              Text(
                'Arabic Name',
                style: const TextStyle(fontSize: 13),
              ),
              const SizedBox(height: 5),
              Container(
                height: 45,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(width: 0.5, color: Colors.grey[300]!),
                  color: Colors.white,
                ),
                child: TextFormField(
                  controller: nameArController,
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(horizontal: 10),
                    hintText: 'Enter Arabic Name',
                    hintStyle: TextStyle(
                      color: Color(0xffB7B7B7),
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 10),

              // Name English
              Text(
                'English Name',
                style: const TextStyle(fontSize: 13),
              ),
              const SizedBox(height: 5),
              Container(
                height: 45,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(width: 0.5, color: Colors.grey[300]!),
                  color: Colors.white,
                ),
                child: TextFormField(
                  controller: nameEnController,
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(horizontal: 10),
                    hintText: 'Enter English Name',
                    hintStyle: TextStyle(
                      color: Color(0xffB7B7B7),
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 10),

              // Floor Plan Image
              Text(
                S.of(context).floorPlanImage,
                style: const TextStyle(fontSize: 13),
              ),
              const SizedBox(height: 5),
              ADFilePicker(
                onSingleFileSelected: (image) {
                  selectedImage.value = image;
                  final updatedPlan = plan.copyWith(image: image);
                  final newList = List<FloorPlanModel>.from(floorPlans.value);
                  if (index < newList.length) {
                    newList[index] = updatedPlan;
                    floorPlans.value = newList;
                  }
                },
                title: S.of(context).Tabheretouploadimage,
                type: FileType.media,
                isMultiple: false,
              ),
            ],
          ),
        );
      },
    );
  }
}
