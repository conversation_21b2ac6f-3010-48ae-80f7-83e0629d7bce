import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../../generated/l10n.dart';
import '../../features/bloc/other_settings_bloc.dart';
import '../../features/models/agent_list_model.dart';
import '../../features/response/agent_list_response.dart';
import '../utils/resources.dart';

class AgentSearchSheet extends HookWidget {
  final AgentListModel? selectedValue;
  final String? label;
  final void Function(AgentListModel?)? onChanged;
  final bool isRequired;

  const AgentSearchSheet({
    super.key,
    required this.onChanged,
    required this.label,
    required this.selectedValue,
    this.isRequired = true,
  });

  @override
  Widget build(BuildContext context) {
    final isDropdownOpen = useState(false);

    return GestureDetector(
      onTap: () {
        isDropdownOpen.value = true;
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          builder: (context) => _buildBottomSheetContent(context, isDropdownOpen),
        );
      },
      child: Container(
        width: MediaQuery.of(context).size.width,
        padding: const EdgeInsets.symmetric(horizontal: 10),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          border: Border.all(width: 0.5, color: Colors.grey[300]!),
          color: Colors.white,
        ),
        height: 50,
        alignment: Alignment.centerLeft,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                selectedValue?.fullname ?? label ?? S.of(context).Agents,
                style: TextStyle(
                  fontSize: 16,
                  color: selectedValue != null ? Colors.black : const Color(0xffB7B7B7),
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const Icon(Icons.keyboard_arrow_down, color: Colors.grey),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomSheetContent(
      BuildContext context, ValueNotifier<bool> isDropdownOpen) {
    return HookBuilder(builder: (context) {
      final searchController = useTextEditingController();
      final filteredData = useState<List<AgentListModel>>([]);
      final allData = useState<List<AgentListModel>>([]);
      final isLoading = useState(true);

      // Load data
      useEffect(() {
        _loadAgents(allData, filteredData, isLoading);
        return null;
      }, []);

      // Search functionality
      useEffect(() {
        _searchAgents(searchController.text, allData.value, filteredData);
        return null;
      }, [searchController.text]);

      return Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                border: Border(bottom: BorderSide(color: Colors.grey, width: 0.2)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    label ?? S.of(context).Agents,
                    style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),

            // Search Field
            Padding(
              padding: const EdgeInsets.all(16),
              child: TextField(
                controller: searchController,
                decoration: InputDecoration(
                  hintText: S.of(context).Search,
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: const BorderSide(color: Colors.grey),
                  ),
                ),
              ),
            ),

            // Content
            Expanded(
              child: isLoading.value
                  ? const Center(child: CircularProgressIndicator())
                  : filteredData.value.isEmpty
                      ? Center(child: Text(S.of(context).Therearenoitems))
                      : ListView.builder(
                          itemCount: filteredData.value.length,
                          itemBuilder: (context, index) {
                            final agent = filteredData.value[index];
                            return ListTile(
                              leading: agent.image != null && agent.image!.isNotEmpty
                                  ? CircleAvatar(
                                      backgroundImage: NetworkImage(agent.image!),
                                      onBackgroundImageError: (_, __) {},
                                      child: agent.image!.isEmpty
                                          ? const Icon(Icons.person)
                                          : null,
                                    )
                                  : const CircleAvatar(
                                      child: Icon(Icons.person),
                                    ),
                              title: Text(agent.fullname ?? ''),
                              subtitle: agent.category?.name != null
                                  ? Text(agent.category!.name!)
                                  : null,
                              onTap: () {
                                onChanged?.call(agent);
                                Navigator.pop(context);
                              },
                              trailing: selectedValue?.id == agent.id
                                  ? const Icon(Icons.check, color: Colors.green)
                                  : null,
                            );
                          },
                        ),
            ),
          ],
        ),
      );
    });
  }

  void _loadAgents(
    ValueNotifier<List<AgentListModel>> allData,
    ValueNotifier<List<AgentListModel>> filteredData,
    ValueNotifier<bool> isLoading,
  ) async {
    try {
      othersettingsbloc.getAgents();
      othersettingsbloc.agents.stream.listen((response) {
        if (response != null && response.code == 1) {
          allData.value = response.agentList;
          filteredData.value = response.agentList;
          isLoading.value = false;
        }
      });
    } catch (e) {
      isLoading.value = false;
    }
  }

  void _searchAgents(
    String query,
    List<AgentListModel> allData,
    ValueNotifier<List<AgentListModel>> filteredData,
  ) {
    if (query.isEmpty) {
      filteredData.value = allData;
    } else {
      filteredData.value = allData
          .where((agent) =>
              agent.fullname?.toLowerCase().contains(query.toLowerCase()) ?? false)
          .toList();
    }
  }
}
