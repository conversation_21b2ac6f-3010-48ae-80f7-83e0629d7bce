import 'dart:io';

import 'package:dotted_border/dotted_border.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';

import '../../core/utils/app_constants.dart';

class ADFilePicker extends StatefulWidget {
  const ADFilePicker(
      {Key? key,
      this.onFilesSelected,
      this.onSingleFileSelected,
      required this.title,
      this.isMultiple = true,
      this.type})
      : super(key: key);
  final Function(List<File> files)? onFilesSelected;
  final Function(File images)? onSingleFileSelected;
  final String title;
  final isMultiple;
  final FileType? type;

  @override
  State<ADFilePicker> createState() => _ADFilePickerState();
}

class _ADFilePickerState extends State<ADFilePicker> {
  List<File> _files = [];
  File? _file;

  @override
  Widget build(BuildContext context) {
    return _files.isNotEmpty || _file != null
        ? SizedBox(
            height: 100,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                widget.isMultiple
                    ? Expanded(
                        flex: 6,
                        child: ListView.separated(
                          itemBuilder: (BuildContext context, int index) {
                            return Container(
                              constraints: const BoxConstraints(maxWidth: 200),
                              child: Chip(
                                label: widget.type == FileType.media
                                    ? Image.file(File(_files[index].path),
                                        height: 40, width: 40)
                                    : Text(_files[index].path.split('/').last),
                                onDeleted: () {
                                  setState(() {
                                    _files.removeAt(index);
                                  });
                                },
                              ),
                            );
                          },
                          scrollDirection: Axis.horizontal,
                          itemCount: _files.length,
                          separatorBuilder: (BuildContext context, int index) =>
                              const SizedBox(width: 10),
                        ),
                      )
                    : Center(
                        child: Container(
                          constraints: const BoxConstraints(maxWidth: 200),
                          child: Chip(
                            label: Text(_file!.path.split('/').last),
                            onDeleted: () {
                              setState(() {
                                _file = null;
                              });
                            },
                          ),
                        ),
                      ),
                if (widget.isMultiple)
                  Expanded(
                      child: IconButton(
                    icon: const Icon(Icons.add),
                    onPressed: _pickFile,
                  ))
              ],
            ),
          )
        : InkWell(
            onTap: _pickFile,
            child: DottedBorder(
              color: const Color(0xffEFEFEF),
              strokeWidth: 1,
              child: Container(
                height: 90,
                decoration: const BoxDecoration(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Center(
                        child: Container(
                      height: 30,
                      width: 30,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          color: const Color(0xffB7B7B7).withOpacity(0.2)),
                      child: Center(
                        child: AppConstants.uploadIcon,
                      ),
                    )),
                    Text(
                      widget.title,
                      style: const TextStyle(
                          color: Color(0xffB7B7B7), fontSize: 14),
                    )
                  ],
                ),
              ),
            ));
  }

  Future<void> _pickFile() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: widget.type!,
      allowMultiple: widget.isMultiple,
    );

    if (result != null) {
      if (!widget.isMultiple) {
        File file = File(result.files.single.path!);
        setState(() {
          _file = file;
        });
        widget.onSingleFileSelected!(_file!);
      } else {
        List<File> files = List<File>.generate(
            result.files.length, (index) => File(result.files[index].path!));
        setState(() {
          _files = [..._files, ...files];
        });
        widget.onFilesSelected!(_files);
      }
    }
  }
}
