<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.dubai.dubai_user_app">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<!--    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />-->

    <application
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher"
        android:label="DXB Admin"
        android:requestLegacyExternalStorage="true"
        android:enableOnBackInvokedCallback="true"
        android:exported="true"
        android:hardwareAccelerated="false"
        android:largeHeap="true"
        android:usesCleartextTraffic="true">
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="AIzaSyAcl3LOVhYIjlWPWjuONGesjhEeDzRPmlY" />

        <activity
            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
            android:exported="false"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
        <activity
            android:name=".MainActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:windowSoftInputMode="adjustResize">

            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme" />
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <!--            <intent-filter>-->
            <!--                <action android:name="android.intent.action.VIEW" />-->
            <!--                <category android:name="android.intent.category.DEFAULT" />-->
            <!--                <category android:name="android.intent.category.BROWSABLE" />-->
            <!--                <data-->
            <!--                    android:scheme="https"-->
            <!--                    android:host="https://dubaiuser.page.link" />-->
            <!--            </intent-filter>-->

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="dubaiuser.page.link"
                    android:scheme="http" />
                <data
                    android:host="dubaiuser.page.link"
                    android:scheme="https" />
            </intent-filter>
        </activity>

        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />

        <provider
                android:name="androidx.core.content.FileProvider"
                android:authorities="${applicationId}.fileprovider"
                android:exported="false"
                android:grantUriPermissions="true" >
            <meta-data
                    android:name="android.support.FILE_PROVIDER_PATHS"
                    android:resource="@xml/file_paths" />
        </provider>
    </application>
    <queries>
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="https" />
        </intent>
    </queries>
</manifest>
